"""
Ultra-fast backtesting engine with vectorized operations and performance optimization.
Supports multiple strategies and comprehensive performance analysis.
"""
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from concurrent.futures import ProcessPoolExecutor, as_completed
from dataclasses import dataclass
import multiprocessing as mp
from loguru import logger
from numba import jit

from strategies.base_strategy import BaseStrategy, Signal, Position
from utils.metrics import AdvancedMetrics
from config.settings import settings


@dataclass
class BacktestResult:
    """Comprehensive backtest results."""
    strategy_name: str
    symbol: str
    timeframe: str
    start_date: str
    end_date: str
    initial_capital: float
    final_capital: float
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_duration: float
    execution_time: float
    trades: List[Dict[str, Any]]
    equity_curve: pd.Series
    parameters: Dict[str, Any]


class VectorizedBacktester:
    """
    High-performance vectorized backtesting engine.
    Optimized for speed and memory efficiency.
    """

    def __init__(self, initial_capital: float = 10000.0):
        self.initial_capital = initial_capital
        self.commission = 0.001  # 0.1% commission
        self.slippage = 0.0005   # 0.05% slippage

    def run_backtest(
        self,
        strategy: BaseStrategy,
        data: pd.DataFrame,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> BacktestResult:
        """
        Run comprehensive backtest for a single strategy.

        Args:
            strategy: Trading strategy to test
            data: Historical market data
            start_date: Backtest start date
            end_date: Backtest end date

        Returns:
            BacktestResult with comprehensive metrics
        """
        start_time = time.perf_counter()

        # Filter data by date range
        if start_date or end_date:
            data = self._filter_data_by_date(data, start_date, end_date)

        if len(data) < 50:
            raise ValueError("Insufficient data for backtesting (minimum 50 points required)")

        logger.info(f"Starting backtest for {strategy.name} with {len(data)} data points")

        # Reset strategy state
        strategy.reset()

        # Initialize tracking variables
        capital = self.initial_capital
        positions = []
        trades = []
        equity_curve = []

        # Vectorized signal generation for performance
        signals = self._generate_signals_vectorized(strategy, data)

        # Process each signal
        for i, (timestamp, row) in enumerate(data.iterrows()):
            current_price = row['close']

            # Update existing positions
            closed_positions = self._update_positions(positions, current_price)

            # Process closed positions
            for position in closed_positions:
                trade = self._close_position(position, current_price, timestamp)
                trades.append(trade)
                capital += trade['pnl'] - self._calculate_costs(trade)
                positions.remove(position)

            # Check for new signals
            if i < len(signals) and signals[i].action != 'HOLD':
                signal = signals[i]
                position = self._open_position(signal, current_price, capital)
                if position:
                    positions.append(position)
                    capital -= position.quantity * current_price + self._calculate_entry_costs(position)

            # Track equity
            unrealized_pnl = sum(pos.unrealized_pnl for pos in positions)
            equity_curve.append(capital + unrealized_pnl)

        # Close remaining positions
        for position in positions:
            trade = self._close_position(position, data.iloc[-1]['close'], data.index[-1])
            trades.append(trade)
            capital += trade['pnl'] - self._calculate_costs(trade)

        execution_time = time.perf_counter() - start_time

        # Calculate performance metrics
        equity_series = pd.Series(equity_curve, index=data.index)
        metrics = self._calculate_comprehensive_metrics(
            trades, equity_series, self.initial_capital
        )

        # Create result object
        result = BacktestResult(
            strategy_name=strategy.name,
            symbol=strategy.symbol,
            timeframe=strategy.timeframe,
            start_date=str(data.index[0].date()),
            end_date=str(data.index[-1].date()),
            initial_capital=self.initial_capital,
            final_capital=capital,
            total_return=metrics['total_return'],
            annual_return=metrics['annual_return'],
            max_drawdown=metrics['max_drawdown'],
            sharpe_ratio=metrics['sharpe_ratio'],
            sortino_ratio=metrics['sortino_ratio'],
            calmar_ratio=metrics['calmar_ratio'],
            win_rate=metrics['win_rate'],
            profit_factor=metrics['profit_factor'],
            total_trades=len(trades),
            avg_trade_duration=metrics['avg_trade_duration'],
            execution_time=execution_time,
            trades=trades,
            equity_curve=equity_series,
            parameters=strategy.parameters
        )

        logger.info(f"Backtest completed in {execution_time:.4f}s - Return: {metrics['total_return']:.2%}")

        return result

    def _generate_signals_vectorized(
        self,
        strategy: BaseStrategy,
        data: pd.DataFrame
    ) -> List[Signal]:
        """Generate signals using vectorized operations where possible."""
        signals = []

        # Initialize strategy with minimum required data
        init_size = min(50, len(data) // 2)
        strategy.initialize(data.head(init_size))

        # Generate signals for each data point
        for i in range(init_size, len(data)):
            window_data = data.iloc[:i+1]
            signal = strategy.process_data(window_data)
            signals.append(signal)

        return signals

    def _filter_data_by_date(
        self,
        data: pd.DataFrame,
        start_date: Optional[str],
        end_date: Optional[str]
    ) -> pd.DataFrame:
        """Filter data by date range."""
        if start_date:
            data = data[data.index >= start_date]
        if end_date:
            data = data[data.index <= end_date]
        return data

    def _update_positions(
        self,
        positions: List[Position],
        current_price: float
    ) -> List[Position]:
        """Update positions and return closed ones."""
        closed_positions = []

        for position in positions:
            position.current_price = current_price

            # Check stop loss and take profit
            if self._should_close_position(position):
                closed_positions.append(position)

        return closed_positions

    def _should_close_position(self, position: Position) -> bool:
        """Check if position should be closed based on stop loss/take profit."""
        if position.current_price is None:
            return False

        if position.side == 'LONG':
            return (
                position.current_price <= position.stop_loss or
                position.current_price >= position.take_profit
            )
        else:
            return (
                position.current_price >= position.stop_loss or
                position.current_price <= position.take_profit
            )

    def _open_position(
        self,
        signal: Signal,
        current_price: float,
        available_capital: float
    ) -> Optional[Position]:
        """Open new position based on signal."""
        # Calculate position size (simplified)
        max_position_value = available_capital * settings.max_position_size
        quantity = max_position_value / current_price

        if quantity <= 0:
            return None

        # Apply slippage
        entry_price = current_price * (1 + self.slippage if signal.action == 'BUY' else 1 - self.slippage)

        position = Position(
            symbol=signal.metadata.get('symbol', 'UNKNOWN'),
            side='LONG' if signal.action == 'BUY' else 'SHORT',
            entry_price=entry_price,
            quantity=quantity,
            entry_time=signal.timestamp,
            stop_loss=self._calculate_stop_loss(entry_price, signal.action),
            take_profit=self._calculate_take_profit(entry_price, signal.action)
        )

        return position

    def _close_position(
        self,
        position: Position,
        exit_price: float,
        exit_time: pd.Timestamp
    ) -> Dict[str, Any]:
        """Close position and create trade record."""
        # Apply slippage
        actual_exit_price = exit_price * (
            1 - self.slippage if position.side == 'LONG' else 1 + self.slippage
        )

        # Calculate P&L
        if position.side == 'LONG':
            pnl = (actual_exit_price - position.entry_price) * position.quantity
        else:
            pnl = (position.entry_price - actual_exit_price) * position.quantity

        trade = {
            'symbol': position.symbol,
            'side': position.side,
            'entry_price': position.entry_price,
            'exit_price': actual_exit_price,
            'quantity': position.quantity,
            'entry_time': position.entry_time,
            'exit_time': exit_time,
            'pnl': pnl,
            'pnl_percentage': (pnl / (position.entry_price * position.quantity)) * 100,
            'duration': exit_time - position.entry_time
        }

        return trade

    def _calculate_stop_loss(self, price: float, action: str) -> float:
        """Calculate stop loss price."""
        if action == 'BUY':
            return price * (1 - settings.stop_loss_percentage)
        else:
            return price * (1 + settings.stop_loss_percentage)

    def _calculate_take_profit(self, price: float, action: str) -> float:
        """Calculate take profit price."""
        if action == 'BUY':
            return price * (1 + settings.take_profit_percentage)
        else:
            return price * (1 - settings.take_profit_percentage)

    def _calculate_entry_costs(self, position: Position) -> float:
        """Calculate entry costs (commission)."""
        return position.entry_price * position.quantity * self.commission

    def _calculate_costs(self, trade: Dict[str, Any]) -> float:
        """Calculate total trading costs."""
        trade_value = trade['exit_price'] * trade['quantity']
        return trade_value * self.commission

    @staticmethod
    def _calculate_drawdown_fast(equity_curve: np.ndarray) -> Tuple[float, np.ndarray]:
        """Fast drawdown calculation with safety checks."""
        if len(equity_curve) == 0:
            return 0.0, np.array([])

        if len(equity_curve) == 1:
            return 0.0, np.array([0.0])

        peak = equity_curve[0]
        drawdown = np.zeros(len(equity_curve))
        max_dd = 0.0

        for i in range(len(equity_curve)):
            if equity_curve[i] > peak:
                peak = equity_curve[i]

            if peak > 0:
                dd = (peak - equity_curve[i]) / peak
            else:
                dd = 0.0

            drawdown[i] = dd

            if dd > max_dd:
                max_dd = dd

        return max_dd, drawdown

    def _calculate_comprehensive_metrics(
        self,
        trades: List[Dict[str, Any]],
        equity_curve: pd.Series,
        initial_capital: float
    ) -> Dict[str, float]:
        """Calculate comprehensive performance metrics with safety checks."""

        # Default metrics for empty results
        default_metrics = {
            'total_return': 0.0,
            'annual_return': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'sortino_ratio': 0.0,
            'calmar_ratio': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'avg_trade_duration': 0.0
        }

        # Check for empty equity curve
        if equity_curve.empty or len(equity_curve) == 0:
            return default_metrics

        # Check for empty trades
        if not trades or len(trades) == 0:
            # Still calculate return metrics from equity curve
            try:
                total_return = (equity_curve.iloc[-1] - initial_capital) / initial_capital
                returns = equity_curve.pct_change().dropna()

                if len(returns) > 1:
                    annual_return = (1 + returns.mean()) ** 252 - 1
                    volatility = returns.std() * np.sqrt(252)

                    # Robust Sharpe ratio calculation with bounds
                    if volatility > 1e-8:  # Minimum volatility threshold
                        sharpe_ratio = annual_return / volatility
                        # Cap Sharpe ratio to reasonable bounds
                        sharpe_ratio = max(-10.0, min(10.0, sharpe_ratio))
                    else:
                        sharpe_ratio = 0.0
                else:
                    annual_return = 0.0
                    sharpe_ratio = 0.0

                max_drawdown, _ = self._calculate_drawdown_fast(equity_curve.values)

                return {
                    'total_return': total_return,
                    'annual_return': annual_return,
                    'max_drawdown': max_drawdown,
                    'sharpe_ratio': sharpe_ratio,
                    'sortino_ratio': 0.0,
                    'calmar_ratio': annual_return / max_drawdown if max_drawdown > 0 else 0.0,
                    'win_rate': 0.0,
                    'profit_factor': 0.0,
                    'avg_trade_duration': 0.0
                }
            except Exception:
                return default_metrics

        try:
            trades_df = pd.DataFrame(trades)

            # Basic metrics
            total_trades = len(trades_df)
            winning_trades = len(trades_df[trades_df['pnl'] > 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            # Return metrics
            total_return = (equity_curve.iloc[-1] - initial_capital) / initial_capital

            # Calculate returns for risk metrics
            returns = equity_curve.pct_change().dropna()

            # Risk metrics
            if len(returns) > 1:
                annual_return = (1 + returns.mean()) ** 252 - 1
                volatility = returns.std() * np.sqrt(252)

                # Robust Sharpe ratio calculation with bounds
                if volatility > 1e-8:  # Minimum volatility threshold
                    sharpe_ratio = annual_return / volatility
                    # Cap Sharpe ratio to reasonable bounds
                    sharpe_ratio = max(-10.0, min(10.0, sharpe_ratio))
                else:
                    sharpe_ratio = 0.0

                # Sortino ratio (downside deviation)
                downside_returns = returns[returns < 0]
                downside_std = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
                if downside_std > 1e-8:
                    sortino_ratio = annual_return / downside_std
                    # Cap Sortino ratio to reasonable bounds
                    sortino_ratio = max(-10.0, min(10.0, sortino_ratio))
                else:
                    sortino_ratio = 0.0
            else:
                annual_return = 0
                sharpe_ratio = 0
                sortino_ratio = 0

            # Drawdown
            max_drawdown, _ = self._calculate_drawdown_fast(equity_curve.values)
            calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else 0

            # Trade metrics
            if winning_trades > 0:
                avg_win = trades_df[trades_df['pnl'] > 0]['pnl'].mean()
            else:
                avg_win = 0

            losing_trades = total_trades - winning_trades
            if losing_trades > 0:
                avg_loss = trades_df[trades_df['pnl'] < 0]['pnl'].mean()
            else:
                avg_loss = 0

            if avg_loss != 0 and losing_trades > 0:
                profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades))
            else:
                profit_factor = 10.0 if avg_win > 0 else 0.0

            # Duration
            if 'duration' in trades_df.columns and not trades_df['duration'].empty:
                avg_trade_duration = trades_df['duration'].mean().total_seconds() / 3600  # hours
            else:
                avg_trade_duration = 0.0

            return {
                'total_return': total_return,
                'annual_return': annual_return,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': calmar_ratio,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'avg_trade_duration': avg_trade_duration
            }

        except Exception as e:
            logger.warning(f"Error calculating metrics: {e}")
            return default_metrics
