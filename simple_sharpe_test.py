"""
Simple Sharpe Ratio Test
========================

Direct test of the Sharpe ratio calculation to verify the fix.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta

def test_sharpe_calculation():
    """Test Sharpe ratio calculation with problematic scenarios."""
    print("🔍 TESTING SHARPE RATIO CALCULATION")
    print("=" * 50)
    
    # Test Case 1: Normal scenario
    print("\n📊 Test Case 1: Normal Returns")
    returns = np.array([0.01, -0.005, 0.02, -0.01, 0.015, -0.008, 0.012])
    annual_return = returns.mean() * 252
    volatility = returns.std() * np.sqrt(252)
    
    print(f"   Annual Return: {annual_return:.4f}")
    print(f"   Volatility: {volatility:.4f}")
    
    # Original calculation (problematic)
    if volatility > 0:
        sharpe_original = annual_return / volatility
    else:
        sharpe_original = 0
    
    # Fixed calculation
    if volatility > 1e-8:
        sharpe_fixed = annual_return / volatility
        sharpe_fixed = max(-10.0, min(10.0, sharpe_fixed))
    else:
        sharpe_fixed = 0.0
    
    print(f"   Original Sharpe: {sharpe_original:.4f}")
    print(f"   Fixed Sharpe: {sharpe_fixed:.4f}")
    
    # Test Case 2: Very low volatility (problematic scenario)
    print("\n⚠️ Test Case 2: Very Low Volatility")
    returns_low_vol = np.array([0.0001, 0.0001, 0.0001, 0.0001, 0.0001])
    annual_return_low = returns_low_vol.mean() * 252
    volatility_low = returns_low_vol.std() * np.sqrt(252)
    
    print(f"   Annual Return: {annual_return_low:.8f}")
    print(f"   Volatility: {volatility_low:.8f}")
    
    # Original calculation (problematic)
    if volatility_low > 0:
        sharpe_original_low = annual_return_low / volatility_low
    else:
        sharpe_original_low = 0
    
    # Fixed calculation
    if volatility_low > 1e-8:
        sharpe_fixed_low = annual_return_low / volatility_low
        sharpe_fixed_low = max(-10.0, min(10.0, sharpe_fixed_low))
    else:
        sharpe_fixed_low = 0.0
    
    print(f"   Original Sharpe: {sharpe_original_low:.2e}")
    print(f"   Fixed Sharpe: {sharpe_fixed_low:.4f}")
    
    # Test Case 3: Zero volatility
    print("\n🚫 Test Case 3: Zero Volatility")
    returns_zero = np.array([0.01, 0.01, 0.01, 0.01, 0.01])
    annual_return_zero = returns_zero.mean() * 252
    volatility_zero = returns_zero.std() * np.sqrt(252)
    
    print(f"   Annual Return: {annual_return_zero:.4f}")
    print(f"   Volatility: {volatility_zero:.8f}")
    
    # Original calculation (problematic)
    if volatility_zero > 0:
        sharpe_original_zero = annual_return_zero / volatility_zero
    else:
        sharpe_original_zero = 0
    
    # Fixed calculation
    if volatility_zero > 1e-8:
        sharpe_fixed_zero = annual_return_zero / volatility_zero
        sharpe_fixed_zero = max(-10.0, min(10.0, sharpe_fixed_zero))
    else:
        sharpe_fixed_zero = 0.0
    
    print(f"   Original Sharpe: {sharpe_original_zero}")
    print(f"   Fixed Sharpe: {sharpe_fixed_zero:.4f}")
    
    print("\n" + "=" * 50)
    print("✅ SHARPE RATIO FIX VERIFICATION COMPLETE")
    print("✅ Fixed version prevents astronomical values")
    print("✅ Bounds ensure realistic Sharpe ratios (-10 to +10)")


def test_with_backtester():
    """Test with actual backtester code."""
    print("\n🔧 TESTING WITH ACTUAL BACKTESTER")
    print("=" * 50)
    
    try:
        from core.backtester import VectorizedBacktester
        
        # Create a simple equity curve that might cause problems
        dates = pd.date_range(start='2023-01-01', periods=100, freq='H')
        
        # Scenario 1: Very small changes (low volatility)
        equity_curve = pd.Series([10000 + i * 0.001 for i in range(100)], index=dates)
        
        backtester = VectorizedBacktester()
        metrics = backtester._calculate_comprehensive_metrics([], equity_curve, 10000.0)
        
        print(f"📊 Low Volatility Test:")
        print(f"   Sharpe Ratio: {metrics['sharpe_ratio']:.4f}")
        print(f"   Total Return: {metrics['total_return']:.4f}")
        
        # Scenario 2: Constant values (zero volatility)
        equity_curve_const = pd.Series([10000] * 100, index=dates)
        metrics_const = backtester._calculate_comprehensive_metrics([], equity_curve_const, 10000.0)
        
        print(f"\n📊 Zero Volatility Test:")
        print(f"   Sharpe Ratio: {metrics_const['sharpe_ratio']:.4f}")
        print(f"   Total Return: {metrics_const['total_return']:.4f}")
        
        print("\n✅ Backtester test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error testing backtester: {e}")


if __name__ == "__main__":
    test_sharpe_calculation()
    test_with_backtester()
